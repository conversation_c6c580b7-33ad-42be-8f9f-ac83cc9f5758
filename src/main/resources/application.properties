spring.application.name=proezedure

# Docker Compose Configuration (disabled by default)
spring.docker.compose.enabled=false

# Database Configuration
spring.datasource.url=*******************************************
spring.datasource.username=proezedure_user
spring.datasource.password=proezedure_pass
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# JSON Support
spring.jpa.properties.hibernate.type.preferred_instant_jdbc_type=TIMESTAMP

# Logging
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
