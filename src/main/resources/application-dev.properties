# Development Profile - PostgreSQL with Docker Compose
spring.docker.compose.enabled=true
spring.docker.compose.file=docker-compose.yml

# JPA Configuration for Development
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Logging
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
