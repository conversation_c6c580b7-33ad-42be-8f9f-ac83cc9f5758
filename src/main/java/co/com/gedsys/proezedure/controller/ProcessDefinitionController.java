package co.com.gedsys.proezedure.controller;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.TemplateStatus;
import co.com.gedsys.proezedure.dto.CreateProcessDefinitionRequest;
import co.com.gedsys.proezedure.dto.ProcessDefinitionResponse;
import co.com.gedsys.proezedure.dto.UpdateProcessDefinitionRequest;
import co.com.gedsys.proezedure.service.ProcessDefinitionService;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/process-definitions")
public class ProcessDefinitionController {

    private final ProcessDefinitionService service;

    public ProcessDefinitionController(ProcessDefinitionService service) {
        this.service = service;
    }

    @PostMapping
    public ResponseEntity<ProcessDefinitionResponse> create(@Valid @RequestBody CreateProcessDefinitionRequest request) {
        ProcessDefinition definition = service.create(
            request.getName(),
            request.getDescription(),
            request.getVersion(),
            request.getDefaultVariables()
        );
        
        ProcessDefinitionResponse response = new ProcessDefinitionResponse(definition);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping
    public ResponseEntity<List<ProcessDefinitionResponse>> findAll(
            @RequestParam(required = false) TemplateStatus status) {
        
        List<ProcessDefinition> definitions;
        if (status != null) {
            definitions = service.findByStatus(status);
        } else {
            definitions = service.findAll();
        }
        
        List<ProcessDefinitionResponse> responses = definitions.stream()
                .map(ProcessDefinitionResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/active")
    public ResponseEntity<List<ProcessDefinitionResponse>> findActive() {
        List<ProcessDefinition> definitions = service.findActiveDefinitions();
        List<ProcessDefinitionResponse> responses = definitions.stream()
                .map(ProcessDefinitionResponse::new)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ProcessDefinitionResponse> findById(@PathVariable UUID id) {
        ProcessDefinition definition = service.findById(id);
        ProcessDefinitionResponse response = new ProcessDefinitionResponse(definition);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/by-name-version")
    public ResponseEntity<ProcessDefinitionResponse> findByNameAndVersion(
            @RequestParam String name,
            @RequestParam String version) {
        
        ProcessDefinition definition = service.findByNameAndVersion(name, version);
        ProcessDefinitionResponse response = new ProcessDefinitionResponse(definition);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProcessDefinitionResponse> update(
            @PathVariable UUID id,
            @Valid @RequestBody UpdateProcessDefinitionRequest request) {
        
        ProcessDefinition definition = service.update(
            id,
            request.getName(),
            request.getDescription(),
            request.getVersion(),
            request.getDefaultVariables()
        );
        
        ProcessDefinitionResponse response = new ProcessDefinitionResponse(definition);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<ProcessDefinitionResponse> updateStatus(
            @PathVariable UUID id,
            @RequestParam TemplateStatus status) {
        
        ProcessDefinition definition = service.updateStatus(id, status);
        ProcessDefinitionResponse response = new ProcessDefinitionResponse(definition);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable UUID id) {
        service.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/count")
    public ResponseEntity<Long> countByStatus(@RequestParam TemplateStatus status) {
        long count = service.countByStatus(status);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/exists")
    public ResponseEntity<Boolean> existsByNameAndVersion(
            @RequestParam String name,
            @RequestParam String version) {
        
        boolean exists = service.existsByNameAndVersion(name, version);
        return ResponseEntity.ok(exists);
    }
}
