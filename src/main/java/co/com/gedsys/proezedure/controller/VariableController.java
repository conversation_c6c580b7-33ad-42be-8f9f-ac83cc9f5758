package co.com.gedsys.proezedure.controller;

import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.dto.*;
import co.com.gedsys.proezedure.service.ProcessInstanceService;
import co.com.gedsys.proezedure.service.VariableService;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api/process-instances")
public class VariableController {

    private final VariableService variableService;
    private final ProcessInstanceService processInstanceService;

    public VariableController(VariableService variableService, ProcessInstanceService processInstanceService) {
        this.variableService = variableService;
        this.processInstanceService = processInstanceService;
    }

    @GetMapping("/{processInstanceId}/variables")
    public ResponseEntity<VariablesResponse> getVariables(@PathVariable UUID processInstanceId) {
        String variables = variableService.getVariables(processInstanceId);
        ProcessInstance instance = processInstanceService.findById(processInstanceId);
        
        VariablesResponse response = new VariablesResponse(
            processInstanceId,
            variables,
            instance.getUpdatedAt()
        );
        
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{processInstanceId}/variables")
    public ResponseEntity<VariablesResponse> updateVariables(
            @PathVariable UUID processInstanceId,
            @Valid @RequestBody UpdateVariablesRequest request) {
        
        String updatedVariables = variableService.updateVariables(processInstanceId, request.getVariables());
        ProcessInstance instance = processInstanceService.findById(processInstanceId);
        
        VariablesResponse response = new VariablesResponse(
            processInstanceId,
            updatedVariables,
            instance.getUpdatedAt()
        );
        
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{processInstanceId}/variables")
    public ResponseEntity<VariablesResponse> patchVariables(
            @PathVariable UUID processInstanceId,
            @Valid @RequestBody PatchVariablesRequest request) {
        
        String updatedVariables;
        if (request.isMerge()) {
            updatedVariables = variableService.mergeVariables(processInstanceId, request.getPatchVariables());
        } else {
            updatedVariables = variableService.patchVariables(processInstanceId, request.getPatchVariables());
        }
        
        ProcessInstance instance = processInstanceService.findById(processInstanceId);
        
        VariablesResponse response = new VariablesResponse(
            processInstanceId,
            updatedVariables,
            instance.getUpdatedAt()
        );
        
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{processInstanceId}/variables")
    public ResponseEntity<Void> clearVariables(@PathVariable UUID processInstanceId) {
        variableService.clearVariables(processInstanceId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{processInstanceId}/variables/{variableName}")
    public ResponseEntity<String> getVariable(
            @PathVariable UUID processInstanceId,
            @PathVariable String variableName) {
        
        if (!variableService.hasVariable(processInstanceId, variableName)) {
            return ResponseEntity.notFound().build();
        }
        
        String variable = variableService.getVariable(processInstanceId, variableName);
        return ResponseEntity.ok(variable);
    }

    @PutMapping("/{processInstanceId}/variables/{variableName}")
    public ResponseEntity<VariablesResponse> setVariable(
            @PathVariable UUID processInstanceId,
            @PathVariable String variableName,
            @Valid @RequestBody SetVariableRequest request) {
        
        String updatedVariables = variableService.setVariable(
            processInstanceId,
            variableName,
            request.getVariableValue()
        );
        
        ProcessInstance instance = processInstanceService.findById(processInstanceId);
        
        VariablesResponse response = new VariablesResponse(
            processInstanceId,
            updatedVariables,
            instance.getUpdatedAt()
        );
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/{processInstanceId}/variables/propagate")
    public ResponseEntity<VariablesResponse> propagateTaskOutputs(
            @PathVariable UUID processInstanceId,
            @Valid @RequestBody PropagateOutputsRequest request) {
        
        String updatedVariables;
        
        if (request.hasCustomOutputs()) {
            updatedVariables = variableService.propagateTaskOutputsWithMapping(
                processInstanceId,
                request.getTaskOutputs(),
                request.getOutputFields()
            );
        } else {
            updatedVariables = variableService.propagateTaskOutputs(
                processInstanceId,
                request.getTaskId(),
                request.getOutputFields()
            );
        }
        
        ProcessInstance instance = processInstanceService.findById(processInstanceId);
        
        VariablesResponse response = new VariablesResponse(
            processInstanceId,
            updatedVariables,
            instance.getUpdatedAt()
        );
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{processInstanceId}/variables/{variableName}/exists")
    public ResponseEntity<Boolean> hasVariable(
            @PathVariable UUID processInstanceId,
            @PathVariable String variableName) {
        
        boolean exists = variableService.hasVariable(processInstanceId, variableName);
        return ResponseEntity.ok(exists);
    }
}
