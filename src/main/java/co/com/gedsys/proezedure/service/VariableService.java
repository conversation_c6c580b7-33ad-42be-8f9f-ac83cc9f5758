package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.domain.Task;
import co.com.gedsys.proezedure.exception.InvalidJsonException;
import co.com.gedsys.proezedure.exception.VariablePropagationException;
import co.com.gedsys.proezedure.util.JsonUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@Transactional
public class VariableService {

    private final ProcessInstanceService processInstanceService;

    public VariableService(ProcessInstanceService processInstanceService) {
        this.processInstanceService = processInstanceService;
    }

    @Transactional(readOnly = true)
    public String getVariables(UUID processInstanceId) {
        ProcessInstance instance = processInstanceService.findById(processInstanceId);
        String variables = instance.getVariables();
        return variables != null ? variables : "{}";
    }

    public String updateVariables(UUID processInstanceId, String variables) {
        validateJsonStructure(variables);
        
        String normalizedVariables = JsonUtils.normalizeJson(variables);
        ProcessInstance instance = processInstanceService.updateVariables(processInstanceId, normalizedVariables);
        return instance.getVariables();
    }

    public String patchVariables(UUID processInstanceId, String patchVariables) {
        validateJsonStructure(patchVariables);
        
        ProcessInstance instance = processInstanceService.findById(processInstanceId);
        String currentVariables = instance.getVariables();
        
        String mergedVariables = JsonUtils.patchJson(currentVariables, patchVariables);
        ProcessInstance updatedInstance = processInstanceService.updateVariables(processInstanceId, mergedVariables);
        return updatedInstance.getVariables();
    }

    public String mergeVariables(UUID processInstanceId, String mergeVariables) {
        validateJsonStructure(mergeVariables);
        
        ProcessInstance instance = processInstanceService.findById(processInstanceId);
        String currentVariables = instance.getVariables();
        
        String mergedVariables = JsonUtils.mergeJson(currentVariables, mergeVariables);
        ProcessInstance updatedInstance = processInstanceService.updateVariables(processInstanceId, mergedVariables);
        return updatedInstance.getVariables();
    }

    public String propagateTaskOutputs(UUID processInstanceId, UUID taskId) {
        return propagateTaskOutputs(processInstanceId, taskId, null);
    }

    public String propagateTaskOutputs(UUID processInstanceId, UUID taskId, String[] outputFields) {
        try {
            ProcessInstance instance = processInstanceService.findById(processInstanceId);
            
            // Para simplificar, asumimos que tenemos acceso a la tarea
            // En una implementación real, necesitaríamos inyectar TaskService
            // Por ahora, implementamos la lógica básica
            
            String currentVariables = instance.getVariables();
            if (currentVariables == null) {
                currentVariables = "{}";
            }
            
            // Esta es una implementación simplificada
            // En la práctica, obtendríamos los outputs de la tarea y los propagaríamos
            String updatedVariables = JsonUtils.mergeJson(currentVariables, "{}");
            
            ProcessInstance updatedInstance = processInstanceService.updateVariables(processInstanceId, updatedVariables);
            return updatedInstance.getVariables();
            
        } catch (Exception e) {
            throw new VariablePropagationException(processInstanceId, "Failed to propagate task outputs", e);
        }
    }

    public String propagateTaskOutputsWithMapping(UUID processInstanceId, String taskOutputs, String[] outputFields) {
        validateJsonStructure(taskOutputs);
        
        try {
            ProcessInstance instance = processInstanceService.findById(processInstanceId);
            String currentVariables = instance.getVariables();
            
            String fieldsToPropagate;
            if (outputFields != null && outputFields.length > 0) {
                fieldsToPropagate = JsonUtils.extractFields(taskOutputs, outputFields);
            } else {
                fieldsToPropagate = taskOutputs;
            }
            
            String mergedVariables = JsonUtils.mergeJson(currentVariables, fieldsToPropagate);
            ProcessInstance updatedInstance = processInstanceService.updateVariables(processInstanceId, mergedVariables);
            return updatedInstance.getVariables();
            
        } catch (Exception e) {
            throw new VariablePropagationException(processInstanceId, "Failed to propagate task outputs with mapping", e);
        }
    }

    public void clearVariables(UUID processInstanceId) {
        processInstanceService.updateVariables(processInstanceId, "{}");
    }

    public boolean hasVariable(UUID processInstanceId, String variableName) {
        String variables = getVariables(processInstanceId);
        try {
            return JsonUtils.extractFields(variables, variableName).contains("\"" + variableName + "\"");
        } catch (Exception e) {
            return false;
        }
    }

    public String getVariable(UUID processInstanceId, String variableName) {
        String variables = getVariables(processInstanceId);
        return JsonUtils.extractFields(variables, variableName);
    }

    public String setVariable(UUID processInstanceId, String variableName, String variableValue) {
        String patchJson = "{\"" + variableName + "\":" + variableValue + "}";
        return patchVariables(processInstanceId, patchJson);
    }

    private void validateJsonStructure(String json) {
        if (json == null) {
            return;
        }
        
        try {
            JsonUtils.validateJson(json);
        } catch (InvalidJsonException e) {
            throw new InvalidJsonException("Invalid JSON structure in variables: " + e.getMessage());
        }
    }
}
