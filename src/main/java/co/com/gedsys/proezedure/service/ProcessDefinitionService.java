package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.TemplateStatus;
import co.com.gedsys.proezedure.exception.DuplicateProcessDefinitionException;
import co.com.gedsys.proezedure.exception.ProcessDefinitionNotFoundException;
import co.com.gedsys.proezedure.repository.ProcessDefinitionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class ProcessDefinitionService {

    private final ProcessDefinitionRepository repository;

    public ProcessDefinitionService(ProcessDefinitionRepository repository) {
        this.repository = repository;
    }

    public ProcessDefinition create(String name, String description, String version, String defaultVariables) {
        validateUniqueNameAndVersion(name, version);
        
        ProcessDefinition definition = new ProcessDefinition(name, description, version, defaultVariables);
        return repository.save(definition);
    }

    @Transactional(readOnly = true)
    public ProcessDefinition findById(UUID id) {
        return repository.findById(id)
                .orElseThrow(() -> new ProcessDefinitionNotFoundException(id));
    }

    @Transactional(readOnly = true)
    public ProcessDefinition findByNameAndVersion(String name, String version) {
        return repository.findByNameAndVersion(name, version)
                .orElseThrow(() -> new ProcessDefinitionNotFoundException(name, version));
    }

    @Transactional(readOnly = true)
    public List<ProcessDefinition> findAll() {
        return repository.findAll();
    }

    @Transactional(readOnly = true)
    public List<ProcessDefinition> findByStatus(TemplateStatus status) {
        return repository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<ProcessDefinition> findActiveDefinitions() {
        return repository.findActiveDefinitionsOrderByCreatedAt(TemplateStatus.ACTIVE);
    }

    public ProcessDefinition update(UUID id, String name, String description, String version, String defaultVariables) {
        ProcessDefinition existing = findById(id);
        
        if (!existing.getName().equals(name) || !existing.getVersion().equals(version)) {
            validateUniqueNameAndVersion(name, version);
        }
        
        existing.setName(name);
        existing.setDescription(description);
        existing.setVersion(version);
        existing.setDefaultVariables(defaultVariables);
        
        return repository.save(existing);
    }

    public ProcessDefinition updateStatus(UUID id, TemplateStatus status) {
        ProcessDefinition definition = findById(id);
        definition.setStatus(status);
        return repository.save(definition);
    }

    public void delete(UUID id) {
        ProcessDefinition definition = findById(id);
        repository.delete(definition);
    }

    @Transactional(readOnly = true)
    public boolean existsByNameAndVersion(String name, String version) {
        return repository.existsByNameAndVersion(name, version);
    }

    @Transactional(readOnly = true)
    public long countByStatus(TemplateStatus status) {
        return repository.countByStatus(status);
    }

    private void validateUniqueNameAndVersion(String name, String version) {
        if (repository.existsByNameAndVersion(name, version)) {
            throw new DuplicateProcessDefinitionException(name, version);
        }
    }
}
