package co.com.gedsys.proezedure.dto;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.TemplateStatus;

import java.time.LocalDateTime;
import java.util.UUID;

public class ProcessDefinitionResponse {

    private UUID id;
    private String name;
    private String description;
    private String version;
    private String defaultVariables;
    private LocalDateTime createdAt;
    private TemplateStatus status;

    public ProcessDefinitionResponse() {
    }

    public ProcessDefinitionResponse(ProcessDefinition definition) {
        this.id = definition.getId();
        this.name = definition.getName();
        this.description = definition.getDescription();
        this.version = definition.getVersion();
        this.defaultVariables = definition.getDefaultVariables();
        this.createdAt = definition.getCreatedAt();
        this.status = definition.getStatus();
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDefaultVariables() {
        return defaultVariables;
    }

    public void setDefaultVariables(String defaultVariables) {
        this.defaultVariables = defaultVariables;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public TemplateStatus getStatus() {
        return status;
    }

    public void setStatus(TemplateStatus status) {
        this.status = status;
    }
}
