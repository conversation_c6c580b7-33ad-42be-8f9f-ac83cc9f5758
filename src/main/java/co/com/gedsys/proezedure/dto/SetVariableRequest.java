package co.com.gedsys.proezedure.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public class SetVariableRequest {

    @NotBlank(message = "Variable name is required")
    private String variableName;

    @NotNull(message = "Variable value is required")
    private Object variableValue;

    public SetVariableRequest() {
    }

    public SetVariableRequest(String variableName, String variableValue) {
        this.variableName = variableName;
        this.variableValue = variableValue;
    }

    public String getVariableName() {
        return variableName;
    }

    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }

    public String getVariableValue() {
        return variableValue;
    }

    public void setVariableValue(String variableValue) {
        this.variableValue = variableValue;
    }
}
