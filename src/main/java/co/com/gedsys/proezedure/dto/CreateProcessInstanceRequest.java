package co.com.gedsys.proezedure.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.LinkedHashMap;
import java.util.UUID;

public class CreateProcessInstanceRequest {

    private UUID templateId;

    @NotBlank(message = "Name is required")
    @Size(max = 255, message = "Name must not exceed 255 characters")
    private String name;

    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;

    private LinkedHashMap<String, Object> variables;

    public CreateProcessInstanceRequest() {
    }

    public CreateProcessInstanceRequest(UUID templateId, String name, String description, String variables) {
        this.templateId = templateId;
        this.name = name;
        this.description = description;
        this.variables = variables;
    }

    public CreateProcessInstanceRequest(String name, String description, String variables) {
        this.name = name;
        this.description = description;
        this.variables = variables;
    }

    public UUID getTemplateId() {
        return templateId;
    }

    public void setTemplateId(UUID templateId) {
        this.templateId = templateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public boolean isFromTemplate() {
        return templateId != null;
    }
}
