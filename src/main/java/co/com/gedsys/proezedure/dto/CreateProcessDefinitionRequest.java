package co.com.gedsys.proezedure.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class CreateProcessDefinitionRequest {

    @NotBlank(message = "Name is required")
    @Size(max = 255, message = "Name must not exceed 255 characters")
    private String name;

    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;

    @NotBlank(message = "Version is required")
    @Size(max = 50, message = "Version must not exceed 50 characters")
    private String version;

    private String defaultVariables;

    public CreateProcessDefinitionRequest() {
    }

    public CreateProcessDefinitionRequest(String name, String description, String version, String defaultVariables) {
        this.name = name;
        this.description = description;
        this.version = version;
        this.defaultVariables = defaultVariables;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDefaultVariables() {
        return defaultVariables;
    }

    public void setDefaultVariables(String defaultVariables) {
        this.defaultVariables = defaultVariables;
    }
}
