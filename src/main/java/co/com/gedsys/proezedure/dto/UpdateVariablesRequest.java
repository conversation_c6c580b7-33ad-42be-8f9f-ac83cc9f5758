package co.com.gedsys.proezedure.dto;

import jakarta.validation.constraints.NotNull;

public class UpdateVariablesRequest {

    @NotNull(message = "Variables are required")
    private String variables;

    public UpdateVariablesRequest() {
    }

    public UpdateVariablesRequest(String variables) {
        this.variables = variables;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }
}
