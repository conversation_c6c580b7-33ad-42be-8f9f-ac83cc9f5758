package co.com.gedsys.proezedure.dto;

import java.time.LocalDateTime;
import java.util.UUID;

public class VariablesResponse {

    private UUID processInstanceId;
    private String variables;
    private LocalDateTime lastUpdated;

    public VariablesResponse() {
    }

    public VariablesResponse(UUID processInstanceId, String variables, LocalDateTime lastUpdated) {
        this.processInstanceId = processInstanceId;
        this.variables = variables;
        this.lastUpdated = lastUpdated;
    }

    public UUID getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(UUID processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
}
