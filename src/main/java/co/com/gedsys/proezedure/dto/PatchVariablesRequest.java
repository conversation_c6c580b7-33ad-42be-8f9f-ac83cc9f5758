package co.com.gedsys.proezedure.dto;

import jakarta.validation.constraints.NotNull;

public class PatchVariablesRequest {

    @NotNull(message = "Patch variables are required")
    private String patchVariables;

    private boolean merge = false;

    public PatchVariablesRequest() {
    }

    public PatchVariablesRequest(String patchVariables) {
        this.patchVariables = patchVariables;
    }

    public PatchVariablesRequest(String patchVariables, boolean merge) {
        this.patchVariables = patchVariables;
        this.merge = merge;
    }

    public String getPatchVariables() {
        return patchVariables;
    }

    public void setPatchVariables(String patchVariables) {
        this.patchVariables = patchVariables;
    }

    public boolean isMerge() {
        return merge;
    }

    public void setMerge(boolean merge) {
        this.merge = merge;
    }
}
