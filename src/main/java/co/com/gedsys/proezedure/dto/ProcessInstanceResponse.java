package co.com.gedsys.proezedure.dto;

import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.domain.ProcessStatus;

import java.time.LocalDateTime;
import java.util.UUID;

public class ProcessInstanceResponse {

    private UUID id;
    private UUID templateId;
    private String name;
    private String description;
    private String variables;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private ProcessStatus status;

    public ProcessInstanceResponse() {
    }

    public ProcessInstanceResponse(ProcessInstance instance) {
        this.id = instance.getId();
        this.templateId = instance.getTemplateId();
        this.name = instance.getName();
        this.description = instance.getDescription();
        this.variables = instance.getVariables();
        this.createdAt = instance.getCreatedAt();
        this.updatedAt = instance.getUpdatedAt();
        this.status = instance.getStatus();
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getTemplateId() {
        return templateId;
    }

    public void setTemplateId(UUID templateId) {
        this.templateId = templateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessStatus status) {
        this.status = status;
    }

    public boolean isFromTemplate() {
        return templateId != null;
    }
}
