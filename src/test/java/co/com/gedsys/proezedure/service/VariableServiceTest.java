package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.exception.InvalidJsonException;
import co.com.gedsys.proezedure.exception.VariablePropagationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VariableServiceTest {

    @Mock
    private ProcessInstanceService processInstanceService;

    @InjectMocks
    private VariableService variableService;

    private ProcessInstance testInstance;
    private UUID processInstanceId;

    @BeforeEach
    void setUp() {
        processInstanceId = UUID.randomUUID();
        testInstance = new ProcessInstance("Test Instance", "Description", "{\"clienteId\": \"12345\"}");
        testInstance.setId(processInstanceId);
    }

    @Test
    void shouldGetVariables() {
        // Given
        when(processInstanceService.findById(processInstanceId)).thenReturn(testInstance);

        // When
        String result = variableService.getVariables(processInstanceId);

        // Then
        assertThat(result).isEqualTo("{\"clienteId\": \"12345\"}");
        verify(processInstanceService).findById(processInstanceId);
    }

    @Test
    void shouldReturnEmptyJsonWhenNoVariables() {
        // Given
        testInstance.setVariables(null);
        when(processInstanceService.findById(processInstanceId)).thenReturn(testInstance);

        // When
        String result = variableService.getVariables(processInstanceId);

        // Then
        assertThat(result).isEqualTo("{}");
        verify(processInstanceService).findById(processInstanceId);
    }

    @Test
    void shouldUpdateVariables() {
        // Given
        String newVariables = "{\"clienteId\": \"67890\", \"estado\": \"activo\"}";
        testInstance.setVariables(newVariables);
        when(processInstanceService.updateVariables(eq(processInstanceId), any(String.class))).thenReturn(testInstance);

        // When
        String result = variableService.updateVariables(processInstanceId, newVariables);

        // Then
        assertThat(result).isEqualTo(newVariables);
        verify(processInstanceService).updateVariables(eq(processInstanceId), any(String.class));
    }

    @Test
    void shouldThrowExceptionForInvalidJson() {
        // Given
        String invalidJson = "{invalid json}";

        // When & Then
        assertThatThrownBy(() -> variableService.updateVariables(processInstanceId, invalidJson))
                .isInstanceOf(InvalidJsonException.class)
                .hasMessageContaining("Invalid JSON structure");

        verify(processInstanceService, never()).updateVariables(any(), any());
    }

    @Test
    void shouldPatchVariables() {
        // Given
        String currentVariables = "{\"clienteId\": \"12345\", \"estado\": \"inicial\"}";
        String patchVariables = "{\"estado\": \"activo\", \"monto\": 50000}";
        String expectedResult = "{\"clienteId\":\"12345\",\"estado\":\"activo\",\"monto\":50000}";
        
        testInstance.setVariables(currentVariables);
        ProcessInstance updatedInstance = new ProcessInstance("Test", "Desc", expectedResult);
        updatedInstance.setId(processInstanceId);
        
        when(processInstanceService.findById(processInstanceId)).thenReturn(testInstance);
        when(processInstanceService.updateVariables(eq(processInstanceId), any(String.class))).thenReturn(updatedInstance);

        // When
        String result = variableService.patchVariables(processInstanceId, patchVariables);

        // Then
        assertThat(result).isEqualTo(expectedResult);
        verify(processInstanceService).findById(processInstanceId);
        verify(processInstanceService).updateVariables(eq(processInstanceId), any(String.class));
    }

    @Test
    void shouldMergeVariables() {
        // Given
        String currentVariables = "{\"clienteId\": \"12345\", \"datos\": {\"nombre\": \"Juan\"}}";
        String mergeVariables = "{\"datos\": {\"apellido\": \"Perez\"}, \"estado\": \"activo\"}";
        String expectedResult = "{\"clienteId\":\"12345\",\"datos\":{\"nombre\":\"Juan\",\"apellido\":\"Perez\"},\"estado\":\"activo\"}";
        
        testInstance.setVariables(currentVariables);
        ProcessInstance updatedInstance = new ProcessInstance("Test", "Desc", expectedResult);
        updatedInstance.setId(processInstanceId);
        
        when(processInstanceService.findById(processInstanceId)).thenReturn(testInstance);
        when(processInstanceService.updateVariables(eq(processInstanceId), any(String.class))).thenReturn(updatedInstance);

        // When
        String result = variableService.mergeVariables(processInstanceId, mergeVariables);

        // Then
        assertThat(result).isEqualTo(expectedResult);
        verify(processInstanceService).findById(processInstanceId);
        verify(processInstanceService).updateVariables(eq(processInstanceId), any(String.class));
    }

    @Test
    void shouldPropagateTaskOutputsWithMapping() {
        // Given
        String taskOutputs = "{\"resultado\": \"exitoso\", \"monto\": 45000, \"observaciones\": \"Todo correcto\"}";
        String[] outputFields = {"resultado", "monto"};
        String currentVariables = "{\"clienteId\": \"12345\"}";
        String expectedResult = "{\"clienteId\":\"12345\",\"resultado\":\"exitoso\",\"monto\":45000}";
        
        testInstance.setVariables(currentVariables);
        ProcessInstance updatedInstance = new ProcessInstance("Test", "Desc", expectedResult);
        updatedInstance.setId(processInstanceId);
        
        when(processInstanceService.findById(processInstanceId)).thenReturn(testInstance);
        when(processInstanceService.updateVariables(eq(processInstanceId), any(String.class))).thenReturn(updatedInstance);

        // When
        String result = variableService.propagateTaskOutputsWithMapping(processInstanceId, taskOutputs, outputFields);

        // Then
        assertThat(result).isEqualTo(expectedResult);
        verify(processInstanceService).findById(processInstanceId);
        verify(processInstanceService).updateVariables(eq(processInstanceId), any(String.class));
    }

    @Test
    void shouldClearVariables() {
        // When
        variableService.clearVariables(processInstanceId);

        // Then
        verify(processInstanceService).updateVariables(processInstanceId, "{}");
    }

    @Test
    void shouldCheckIfVariableExists() {
        // Given
        String variables = "{\"clienteId\": \"12345\", \"estado\": \"activo\"}";
        testInstance.setVariables(variables);
        when(processInstanceService.findById(processInstanceId)).thenReturn(testInstance);

        // When
        boolean exists = variableService.hasVariable(processInstanceId, "clienteId");
        boolean notExists = variableService.hasVariable(processInstanceId, "nonexistent");

        // Then
        assertThat(exists).isTrue();
        assertThat(notExists).isFalse();
        verify(processInstanceService, times(2)).findById(processInstanceId);
    }

    @Test
    void shouldGetSpecificVariable() {
        // Given
        String variables = "{\"clienteId\": \"12345\", \"estado\": \"activo\"}";
        testInstance.setVariables(variables);
        when(processInstanceService.findById(processInstanceId)).thenReturn(testInstance);

        // When
        String result = variableService.getVariable(processInstanceId, "clienteId");

        // Then
        assertThat(result).contains("clienteId");
        verify(processInstanceService).findById(processInstanceId);
    }

    @Test
    void shouldSetSpecificVariable() {
        // Given
        String currentVariables = "{\"clienteId\": \"12345\"}";
        String expectedResult = "{\"clienteId\":\"12345\",\"estado\":\"activo\"}";
        
        testInstance.setVariables(currentVariables);
        ProcessInstance updatedInstance = new ProcessInstance("Test", "Desc", expectedResult);
        updatedInstance.setId(processInstanceId);
        
        when(processInstanceService.findById(processInstanceId)).thenReturn(testInstance);
        when(processInstanceService.updateVariables(eq(processInstanceId), any(String.class))).thenReturn(updatedInstance);

        // When
        String result = variableService.setVariable(processInstanceId, "estado", "\"activo\"");

        // Then
        assertThat(result).isEqualTo(expectedResult);
        verify(processInstanceService).findById(processInstanceId);
        verify(processInstanceService).updateVariables(eq(processInstanceId), any(String.class));
    }
}
