package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.domain.ProcessStatus;
import co.com.gedsys.proezedure.exception.ProcessInstanceNotFoundException;
import co.com.gedsys.proezedure.repository.ProcessInstanceRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProcessInstanceServiceTest {

    @Mock
    private ProcessInstanceRepository repository;

    @Mock
    private ProcessDefinitionService processDefinitionService;

    @Mock
    private ValidationService validationService;

    @InjectMocks
    private ProcessInstanceService service;

    private ProcessInstance testInstance;
    private ProcessDefinition testDefinition;
    private UUID instanceId;
    private UUID templateId;

    @BeforeEach
    void setUp() {
        instanceId = UUID.randomUUID();
        templateId = UUID.randomUUID();
        
        testDefinition = new ProcessDefinition("Test Process", "Description", "1.0", "{\"default\": \"value\"}");
        testDefinition.setId(templateId);
        
        testInstance = new ProcessInstance(templateId, "Test Instance", "Instance Description", "{}");
        testInstance.setId(instanceId);
    }

    @Test
    void shouldCreateInstanceFromTemplate() {
        // Given
        when(processDefinitionService.findById(templateId)).thenReturn(testDefinition);
        when(repository.save(any(ProcessInstance.class))).thenReturn(testInstance);

        // When
        ProcessInstance result = service.createFromTemplate(templateId, "Test Instance", "Instance Description", "{}");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTemplateId()).isEqualTo(templateId);
        assertThat(result.getName()).isEqualTo("Test Instance");
        verify(processDefinitionService).findById(templateId);
        verify(repository).save(any(ProcessInstance.class));
    }

    @Test
    void shouldCreateAdHocInstance() {
        // Given
        ProcessInstance adHocInstance = new ProcessInstance("Ad-hoc Instance", "Description", "{}");
        when(repository.save(any(ProcessInstance.class))).thenReturn(adHocInstance);

        // When
        ProcessInstance result = service.createAdHoc("Ad-hoc Instance", "Description", "{}");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTemplateId()).isNull();
        assertThat(result.getName()).isEqualTo("Ad-hoc Instance");
        verify(repository).save(any(ProcessInstance.class));
        verifyNoInteractions(processDefinitionService);
    }

    @Test
    void shouldFindProcessInstanceById() {
        // Given
        when(repository.findById(instanceId)).thenReturn(Optional.of(testInstance));

        // When
        ProcessInstance result = service.findById(instanceId);

        // Then
        assertThat(result).isEqualTo(testInstance);
        verify(repository).findById(instanceId);
    }

    @Test
    void shouldThrowExceptionWhenInstanceNotFound() {
        // Given
        when(repository.findById(instanceId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> service.findById(instanceId))
                .isInstanceOf(ProcessInstanceNotFoundException.class)
                .hasMessageContaining(instanceId.toString());

        verify(repository).findById(instanceId);
    }

    @Test
    void shouldUpdateVariables() {
        // Given
        String newVariables = "{\"updated\": \"variables\"}";
        when(repository.findById(instanceId)).thenReturn(Optional.of(testInstance));
        when(repository.save(testInstance)).thenReturn(testInstance);

        // When
        ProcessInstance result = service.updateVariables(instanceId, newVariables);

        // Then
        assertThat(result.getVariables()).isEqualTo(newVariables);
        verify(repository).findById(instanceId);
        verify(repository).save(testInstance);
    }

    @Test
    void shouldUpdateStatus() {
        // Given
        when(repository.findById(instanceId)).thenReturn(Optional.of(testInstance));
        when(repository.save(testInstance)).thenReturn(testInstance);

        // When
        ProcessInstance result = service.updateStatus(instanceId, ProcessStatus.COMPLETED);

        // Then
        assertThat(result.getStatus()).isEqualTo(ProcessStatus.COMPLETED);
        verify(repository).findById(instanceId);
        verify(repository).save(testInstance);
    }

    @Test
    void shouldDeleteProcessInstance() {
        // Given
        when(repository.findById(instanceId)).thenReturn(Optional.of(testInstance));

        // When
        service.delete(instanceId);

        // Then
        verify(repository).findById(instanceId);
        verify(repository).delete(testInstance);
    }
}
