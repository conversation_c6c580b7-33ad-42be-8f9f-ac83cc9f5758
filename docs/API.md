# API REST - Documentación Completa

## 📋 Índice

- [Autenticación](#autenticación)
- [Códigos de Estado](#códigos-de-estado)
- [Plantillas de Proceso](#plantillas-de-proceso)
- [Instancias de Proceso](#instancias-de-proceso)
- [Variables de Proceso](#variables-de-proceso)
- [Gestión de Tareas](#gestión-de-tareas)
- [Gestión de Subtareas](#gestión-de-subtareas)
- [Ejemplos de Uso](#ejemplos-de-uso)

## 🔐 Autenticación

Actualmente el sistema no requiere autenticación. En futuras versiones se implementará JWT/OAuth2.

## 📊 Códigos de Estado

| Código | Descripción | Uso |
|--------|-------------|-----|
| 200 | OK | Operación exitosa |
| 201 | Created | Recurso creado exitosamente |
| 204 | No Content | Operación exitosa sin contenido |
| 400 | Bad Request | Datos inválidos o validaciones fallidas |
| 404 | Not Found | Recurso no encontrado |
| 409 | Conflict | Conflicto (ej: FormKey duplicado) |
| 500 | Internal Server Error | Error interno del servidor |

## 🏗️ Plantillas de Proceso

### Crear Plantilla de Proceso
```http
POST /api/process-definitions
Content-Type: application/json

{
  "name": "Proceso de Aprobación",
  "description": "Proceso para aprobar solicitudes",
  "version": "1.0",
  "defaultVariables": "{\"clienteId\": null, \"monto\": 0}"
}
```

**Respuesta (201):**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Proceso de Aprobación",
  "description": "Proceso para aprobar solicitudes",
  "version": "1.0",
  "defaultVariables": "{\"clienteId\": null, \"monto\": 0}",
  "status": "ACTIVE",
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### Listar Plantillas de Proceso
```http
GET /api/process-definitions
```

### Obtener Plantilla por ID
```http
GET /api/process-definitions/{id}
```

### Actualizar Plantilla
```http
PUT /api/process-definitions/{id}
Content-Type: application/json

{
  "name": "Proceso de Aprobación Actualizado",
  "description": "Descripción actualizada",
  "version": "1.1",
  "defaultVariables": "{\"clienteId\": null, \"monto\": 0, \"estado\": \"inicial\"}"
}
```

### Cambiar Estado de Plantilla
```http
PATCH /api/process-definitions/{id}/status
Content-Type: application/json

{
  "status": "INACTIVE"
}
```

### Eliminar Plantilla
```http
DELETE /api/process-definitions/{id}
```

## 🔄 Instancias de Proceso

### Crear Instancia de Proceso
```http
POST /api/process-instances
Content-Type: application/json

{
  "templateId": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Solicitud de Crédito #001",
  "description": "Solicitud de crédito para cliente Juan Pérez",
  "variables": "{\"clienteId\": \"12345\", \"monto\": 50000}"
}
```

### Crear Instancia Sin Plantilla
```http
POST /api/process-instances/standalone
Content-Type: application/json

{
  "name": "Proceso Ad-hoc",
  "description": "Proceso creado dinámicamente",
  "variables": "{\"tipo\": \"adhoc\"}"
}
```

### Listar Instancias
```http
GET /api/process-instances
```

### Filtrar por Estado
```http
GET /api/process-instances?status=RUNNING
```

### Obtener Instancia por ID
```http
GET /api/process-instances/{id}
```

### Actualizar Instancia
```http
PUT /api/process-instances/{id}
Content-Type: application/json

{
  "name": "Solicitud Actualizada",
  "description": "Descripción actualizada"
}
```

### Cambiar Estado de Instancia
```http
PATCH /api/process-instances/{id}/status
Content-Type: application/json

{
  "status": "COMPLETED"
}
```

## 📊 Variables de Proceso

### Obtener Variables
```http
GET /api/process-instances/{id}/variables
```

**Respuesta:**
```json
{
  "processInstanceId": "123e4567-e89b-12d3-a456-426614174000",
  "variables": "{\"clienteId\": \"12345\", \"monto\": 50000, \"estado\": \"aprobado\"}",
  "lastUpdated": "2024-01-15T10:30:00Z"
}
```

### Actualizar Variables Completas
```http
PUT /api/process-instances/{id}/variables
Content-Type: application/json

{
  "variables": "{\"clienteId\": \"12345\", \"monto\": 75000, \"estado\": \"revisado\"}"
}
```

### Actualizar Variables Parciales (Patch)
```http
PATCH /api/process-instances/{id}/variables
Content-Type: application/json

{
  "patchVariables": "{\"estado\": \"aprobado\", \"fechaAprobacion\": \"2024-01-15\"}",
  "merge": false
}
```

### Actualizar Variables Parciales (Merge)
```http
PATCH /api/process-instances/{id}/variables
Content-Type: application/json

{
  "patchVariables": "{\"datos\": {\"apellido\": \"García\"}}",
  "merge": true
}
```

### Obtener Variable Específica
```http
GET /api/process-instances/{id}/variables/clienteId
```

### Establecer Variable Específica
```http
PUT /api/process-instances/{id}/variables/estado
Content-Type: application/json

{
  "variableName": "estado",
  "variableValue": "\"completado\""
}
```

### Verificar Existencia de Variable
```http
GET /api/process-instances/{id}/variables/clienteId/exists
```

### Propagar Outputs de Tarea
```http
POST /api/process-instances/{id}/variables/propagate
Content-Type: application/json

{
  "taskId": "456e7890-e89b-12d3-a456-426614174000",
  "taskOutputs": "{\"resultado\": \"aprobado\", \"monto\": 60000}",
  "outputFields": ["resultado", "monto"]
}
```

### Limpiar Variables
```http
DELETE /api/process-instances/{id}/variables
```

## 📋 Gestión de Tareas

### Crear Tarea
```http
POST /api/tasks
Content-Type: application/json

{
  "processInstanceId": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Revisar Documentos",
  "description": "Revisar documentos del cliente",
  "formKey": "review-documents",
  "order": 1,
  "inputs": "{\"documentos\": [\"cedula\", \"ingresos\"]}"
}
```

### Crear Tarea en Proceso Específico
```http
POST /api/process-instances/{processId}/tasks
Content-Type: application/json

{
  "name": "Aprobar Solicitud",
  "description": "Aprobar o rechazar la solicitud",
  "formKey": "approve-request",
  "order": 2,
  "inputs": "{\"requiereAprobacion\": true}"
}
```

### Listar Tareas
```http
GET /api/tasks
```

### Filtrar Tareas
```http
GET /api/tasks?status=IN_PROGRESS&assignedTo=jmarin
```

### Tareas Disponibles
```http
GET /api/tasks/available
```

### Obtener Tarea por ID
```http
GET /api/tasks/{id}
```

### Actualizar Tarea
```http
PUT /api/tasks/{id}
Content-Type: application/json

{
  "name": "Revisar Documentos Actualizado",
  "description": "Descripción actualizada",
  "formKey": "review-documents-v2",
  "order": 1,
  "inputs": "{\"documentos\": [\"cedula\", \"ingresos\", \"referencias\"]}"
}
```

### Asignar Tarea
```http
PATCH /api/tasks/{id}/assign
Content-Type: application/json

{
  "assignedTo": "jmarin"
}
```

### Reclamar Tarea
```http
PATCH /api/tasks/{id}/claim
Content-Type: application/json

{
  "username": "jmarin"
}
```

### Completar Tarea
```http
PATCH /api/tasks/{id}/complete
Content-Type: application/json

{
  "username": "jmarin",
  "outputs": "{\"aprobado\": true, \"observaciones\": \"Documentos en orden\"}"
}
```

### Actualizar Estado de Tarea
```http
PATCH /api/tasks/{id}/status
Content-Type: application/json

{
  "status": "IN_PROGRESS"
}
```

### Actualizar Inputs de Tarea
```http
PATCH /api/tasks/{id}/inputs
Content-Type: application/json

{
  "inputs": "{\"documentos\": [\"cedula\", \"ingresos\", \"referencias\", \"autorizacion\"]}"
}
```

### Actualizar Outputs de Tarea
```http
PATCH /api/tasks/{id}/outputs
Content-Type: application/json

{
  "outputs": "{\"resultado\": \"aprobado\", \"monto\": 60000, \"observaciones\": \"Todo correcto\"}"
}
```

### Eliminar Tarea
```http
DELETE /api/tasks/{id}
```

## 👤 Tareas por Usuario

### Obtener Tareas de Usuario
```http
GET /api/users/{username}/tasks
```

### Tareas Pendientes de Usuario
```http
GET /api/users/{username}/tasks/pending
```

### Tareas En Progreso de Usuario
```http
GET /api/users/{username}/tasks/in-progress
```

### Tareas Completadas de Usuario
```http
GET /api/users/{username}/tasks/completed
```

## 📋 Tareas por Proceso

### Obtener Tareas de Proceso
```http
GET /api/process-instances/{processId}/tasks
```

### Próxima Tarea en Proceso
```http
GET /api/process-instances/{processId}/tasks/next
```

## 📝 Gestión de Subtareas

### Crear Subtarea
```http
POST /api/subtasks
Content-Type: application/json

{
  "taskId": "456e7890-e89b-12d3-a456-426614174000",
  "name": "Verificar Cédula",
  "description": "Verificar autenticidad de la cédula",
  "formKey": "verify-id",
  "order": 1,
  "inputs": "{\"tipoDocumento\": \"cedula\"}"
}
```

### Crear Subtarea en Tarea Específica
```http
POST /api/tasks/{taskId}/subtasks
Content-Type: application/json

{
  "name": "Verificar Ingresos",
  "description": "Verificar comprobantes de ingresos",
  "formKey": "verify-income",
  "order": 2,
  "inputs": "{\"tipoComprobante\": \"nomina\"}"
}
```

### Listar Subtareas
```http
GET /api/subtasks
```

### Filtrar Subtareas
```http
GET /api/subtasks?status=COMPLETED&taskId=456e7890-e89b-12d3-a456-426614174000
```

### Obtener Subtarea por ID
```http
GET /api/subtasks/{id}
```

### Actualizar Subtarea
```http
PUT /api/subtasks/{id}
Content-Type: application/json

{
  "name": "Verificar Cédula Actualizado",
  "description": "Descripción actualizada",
  "formKey": "verify-id-v2",
  "order": 1,
  "inputs": "{\"tipoDocumento\": \"cedula\", \"verificacionBiometrica\": true}"
}
```

### Completar Subtarea
```http
PATCH /api/subtasks/{id}/complete
Content-Type: application/json

{
  "outputs": "{\"verificado\": true, \"observaciones\": \"Documento válido\"}"
}
```

### Obtener Subtareas de Tarea
```http
GET /api/tasks/{taskId}/subtasks
```

### Eliminar Subtarea
```http
DELETE /api/subtasks/{id}
```

## 📚 Ejemplos de Uso

### Flujo Completo: Proceso de Aprobación

#### 1. Crear Plantilla
```bash
curl -X POST http://localhost:8080/api/process-definitions \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Proceso de Crédito",
    "description": "Proceso para aprobar créditos",
    "version": "1.0",
    "defaultVariables": "{\"clienteId\": null, \"monto\": 0, \"estado\": \"inicial\"}"
  }'
```

#### 2. Crear Instancia
```bash
curl -X POST http://localhost:8080/api/process-instances \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Crédito Juan Pérez",
    "description": "Solicitud de crédito personal",
    "variables": "{\"clienteId\": \"12345\", \"monto\": 50000}"
  }'
```

#### 3. Crear Tarea
```bash
curl -X POST http://localhost:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "processInstanceId": "456e7890-e89b-12d3-a456-426614174000",
    "name": "Revisar Documentos",
    "description": "Revisar documentación del cliente",
    "formKey": "review-docs",
    "order": 1,
    "inputs": "{\"documentos\": [\"cedula\", \"ingresos\"]}"
  }'
```

#### 4. Asignar y Completar Tarea
```bash
# Asignar
curl -X PATCH http://localhost:8080/api/tasks/789e0123-e89b-12d3-a456-426614174000/assign \
  -H "Content-Type: application/json" \
  -d '{"assignedTo": "jmarin"}'

# Completar
curl -X PATCH http://localhost:8080/api/tasks/789e0123-e89b-12d3-a456-426614174000/complete \
  -H "Content-Type: application/json" \
  -d '{
    "username": "jmarin",
    "outputs": "{\"aprobado\": true, \"observaciones\": \"Documentos correctos\"}"
  }'
```

#### 5. Verificar Variables Actualizadas
```bash
curl -X GET http://localhost:8080/api/process-instances/456e7890-e89b-12d3-a456-426614174000/variables
```

### Ejemplo de JSON para Variables
```json
{
  "clienteId": "12345",
  "datosPersonales": {
    "nombre": "Juan",
    "apellido": "Pérez",
    "edad": 35
  },
  "solicitud": {
    "monto": 50000,
    "plazo": 24,
    "proposito": "vivienda"
  },
  "estado": "en_revision",
  "fechas": {
    "solicitud": "2024-01-15",
    "revision": "2024-01-16"
  },
  "documentos": ["cedula", "ingresos", "referencias"],
  "aprobaciones": []
}
```

## ⚠️ Validaciones y Errores

### Errores Comunes

#### FormKey Duplicado (409)
```json
{
  "status": 409,
  "error": "Duplicate Form Key",
  "message": "Duplicate formKey 'review-docs' for Task in context 456e7890-e89b-12d3-a456-426614174000",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### JSON Inválido (400)
```json
{
  "status": 400,
  "error": "JSON Schema Validation Failed",
  "message": "JSON schema validation failed for field 'inputs': expected valid JSON, but got {invalid}",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Transición de Estado Inválida (400)
```json
{
  "status": 400,
  "error": "Invalid State Transition",
  "message": "Invalid state transition for Task: cannot change from COMPLETED to IN_PROGRESS",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Tarea No Asignada (400)
```json
{
  "status": 400,
  "error": "Task Not Assigned",
  "message": "Task 789e0123-e89b-12d3-a456-426614174000 is not assigned to user jmarin",
  "timestamp": "2024-01-15T10:30:00Z"
}
```
