{"openapi": "3.0.3", "info": {"title": "Proezedure API", "description": "API REST para gestión de procesos, tareas y subtareas con soporte para variables dinámicas", "version": "1.0.0", "contact": {"name": "Gedsys Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "Servidor de desarrollo local"}], "paths": {"/api/process-definitions": {"get": {"tags": ["Process Definitions"], "summary": "Listar plantillas de proceso", "operationId": "listProcessDefinitions", "responses": {"200": {"description": "Lista de plantillas de proceso", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessDefinition"}}}}}}}, "post": {"tags": ["Process Definitions"], "summary": "Crear plantilla de proceso", "operationId": "createProcessDefinition", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProcessDefinitionRequest"}}}}, "responses": {"201": {"description": "Plantilla creada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessDefinition"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}}}}, "/api/process-definitions/{id}": {"get": {"tags": ["Process Definitions"], "summary": "Obtener plantilla por ID", "operationId": "getProcessDefinitionById", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "responses": {"200": {"description": "Plantilla encontrada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessDefinition"}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"tags": ["Process Definitions"], "summary": "Actualizar plantilla", "operationId": "updateProcessDefinition", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProcessDefinitionRequest"}}}}, "responses": {"200": {"description": "Plantilla actualizada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessDefinition"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "delete": {"tags": ["Process Definitions"], "summary": "Eliminar plantilla", "operationId": "deleteProcessDefinition", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "responses": {"204": {"description": "Plantilla eliminada"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/process-definitions/{id}/status": {"patch": {"tags": ["Process Definitions"], "summary": "Cambiar estado de plantilla", "operationId": "updateProcessDefinitionStatus", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusUpdateRequest"}}}}, "responses": {"200": {"description": "Estado actualizado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessDefinition"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/process-instances": {"get": {"tags": ["Process Instances"], "summary": "Listar instancias de proceso", "operationId": "listProcessInstances", "parameters": [{"name": "status", "in": "query", "schema": {"$ref": "#/components/schemas/ProcessInstanceStatus"}, "description": "Filtrar por estado"}], "responses": {"200": {"description": "Lista de instancias de proceso", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessInstance"}}}}}}}, "post": {"tags": ["Process Instances"], "summary": "Crear instancia de proceso", "operationId": "createProcessInstance", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProcessInstanceRequest"}}}}, "responses": {"201": {"description": "Instancia creada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessInstance"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}}}}, "/api/process-instances/standalone": {"post": {"tags": ["Process Instances"], "summary": "Crear instancia sin plantilla", "operationId": "createStandaloneProcessInstance", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStandaloneProcessInstanceRequest"}}}}, "responses": {"201": {"description": "Instancia standalone creada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessInstance"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}}}}, "/api/process-instances/{id}": {"get": {"tags": ["Process Instances"], "summary": "Obtener instancia por ID", "operationId": "getProcessInstanceById", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "responses": {"200": {"description": "Instancia encontrada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessInstance"}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"tags": ["Process Instances"], "summary": "Actualizar instancia", "operationId": "updateProcessInstance", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProcessInstanceRequest"}}}}, "responses": {"200": {"description": "Instancia actualizada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessInstance"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/process-instances/{id}/status": {"patch": {"tags": ["Process Instances"], "summary": "Cambiar estado de instancia", "operationId": "updateProcessInstanceStatus", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessInstanceStatusUpdateRequest"}}}}, "responses": {"200": {"description": "Estado actualizado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessInstance"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/process-instances/{id}/variables": {"get": {"tags": ["Process Variables"], "summary": "Obtener variables de proceso", "operationId": "getProcessVariables", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "responses": {"200": {"description": "Variables del proceso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessVariables"}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"tags": ["Process Variables"], "summary": "Actualizar variables completas", "operationId": "updateProcessVariables", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVariablesRequest"}}}}, "responses": {"200": {"description": "Variables actualizadas", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessVariables"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "patch": {"tags": ["Process Variables"], "summary": "Actualizar variables parciales", "operationId": "patchProcessVariables", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchVariablesRequest"}}}}, "responses": {"200": {"description": "Variables actualizadas parcialmente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessVariables"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "delete": {"tags": ["Process Variables"], "summary": "Limpiar variables", "operationId": "clearProcessVariables", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "responses": {"204": {"description": "Variables limpiadas"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/process-instances/{id}/variables/{variableName}": {"get": {"tags": ["Process Variables"], "summary": "Obtener variable específica", "operationId": "getSpecificVariable", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}, {"name": "variableName", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Nombre de la variable"}], "responses": {"200": {"description": "Valor de la variable", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"tags": ["Process Variables"], "summary": "Establecer variable específica", "operationId": "setSpecificVariable", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}, {"name": "variableName", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Nombre de la variable"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetVariableRequest"}}}}, "responses": {"200": {"description": "Variable establecida", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/tasks": {"get": {"tags": ["Tasks"], "summary": "<PERSON>ar tareas", "operationId": "listTasks", "parameters": [{"name": "status", "in": "query", "schema": {"$ref": "#/components/schemas/TaskStatus"}, "description": "Filtrar por estado"}, {"name": "assignedTo", "in": "query", "schema": {"type": "string"}, "description": "Filtrar por usuario asignado"}], "responses": {"200": {"description": "Lista de tareas", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Task"}}}}}}}, "post": {"tags": ["Tasks"], "summary": "<PERSON><PERSON><PERSON> tarea", "operationId": "createTask", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTaskRequest"}}}}, "responses": {"201": {"description": "Tarea creada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Task"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "409": {"$ref": "#/components/responses/Conflict"}}}}, "/api/tasks/available": {"get": {"tags": ["Tasks"], "summary": "Obtener tareas disponibles", "operationId": "getAvailableTasks", "responses": {"200": {"description": "Lista de tareas disponibles", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Task"}}}}}}}}, "/api/tasks/{id}": {"get": {"tags": ["Tasks"], "summary": "Obtener tarea por ID", "operationId": "getTaskById", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "responses": {"200": {"description": "Tarea encontrada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Task"}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"tags": ["Tasks"], "summary": "Actualizar tarea", "operationId": "updateTask", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaskRequest"}}}}, "responses": {"200": {"description": "<PERSON>rea <PERSON>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Task"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "delete": {"tags": ["Tasks"], "summary": "Eliminar tarea", "operationId": "deleteTask", "parameters": [{"$ref": "#/components/parameters/UuidPathParam"}], "responses": {"204": {"description": "<PERSON><PERSON> eliminada"}, "404": {"$ref": "#/components/responses/NotFound"}}}}}, "components": {"schemas": {"ProcessDefinition": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Identificador único de la plantilla"}, "name": {"type": "string", "description": "Nombre de la plantilla"}, "description": {"type": "string", "description": "Descripción de la plantilla"}, "version": {"type": "string", "description": "Versión de la plantilla"}, "defaultVariables": {"type": "string", "description": "Variables por defecto en formato JSON"}, "status": {"$ref": "#/components/schemas/ProcessDefinitionStatus"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha de creación"}}, "required": ["id", "name", "version", "status", "createdAt"]}, "CreateProcessDefinitionRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Nombre de la plantilla"}, "description": {"type": "string", "description": "Descripción de la plantilla"}, "version": {"type": "string", "description": "Versión de la plantilla"}, "defaultVariables": {"type": "string", "description": "Variables por defecto en formato JSON"}}, "required": ["name", "version"]}, "UpdateProcessDefinitionRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Nombre de la plantilla"}, "description": {"type": "string", "description": "Descripción de la plantilla"}, "version": {"type": "string", "description": "Versión de la plantilla"}, "defaultVariables": {"type": "string", "description": "Variables por defecto en formato JSON"}}}, "ProcessDefinitionStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"], "description": "Estado de la plantilla de proceso"}, "StatusUpdateRequest": {"type": "object", "properties": {"status": {"type": "string", "description": "Nuevo estado"}}, "required": ["status"]}, "ProcessInstance": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Identificador único de la instancia"}, "templateId": {"type": "string", "format": "uuid", "description": "ID de la plantilla asociada", "nullable": true}, "name": {"type": "string", "description": "Nombre de la instancia"}, "description": {"type": "string", "description": "Descripción de la instancia"}, "variables": {"type": "string", "description": "Variables de la instancia en formato JSON"}, "status": {"$ref": "#/components/schemas/ProcessInstanceStatus"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha de creación"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Fecha de última actualización"}}, "required": ["id", "name", "status", "createdAt", "updatedAt"]}, "CreateProcessInstanceRequest": {"type": "object", "properties": {"templateId": {"type": "string", "format": "uuid", "description": "ID de la plantilla a usar"}, "name": {"type": "string", "description": "Nombre de la instancia"}, "description": {"type": "string", "description": "Descripción de la instancia"}, "variables": {"type": "string", "description": "Variables iniciales en formato JSON"}}, "required": ["templateId", "name"]}, "CreateStandaloneProcessInstanceRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Nombre de la instancia"}, "description": {"type": "string", "description": "Descripción de la instancia"}, "variables": {"type": "string", "description": "Variables iniciales en formato JSON"}}, "required": ["name"]}, "UpdateProcessInstanceRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Nombre de la instancia"}, "description": {"type": "string", "description": "Descripción de la instancia"}}}, "ProcessInstanceStatus": {"type": "string", "enum": ["RUNNING", "COMPLETED", "CANCELLED"], "description": "Estado de la instancia de proceso"}, "ProcessInstanceStatusUpdateRequest": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ProcessInstanceStatus"}}, "required": ["status"]}, "ProcessVariables": {"type": "object", "properties": {"processInstanceId": {"type": "string", "format": "uuid", "description": "ID de la instancia de proceso"}, "variables": {"type": "string", "description": "Variables en formato JSON"}, "lastUpdated": {"type": "string", "format": "date-time", "description": "Fecha de última actualización"}}, "required": ["processInstanceId", "variables", "lastUpdated"]}, "UpdateVariablesRequest": {"type": "object", "properties": {"variables": {"type": "string", "description": "Variables completas en formato JSON"}}, "required": ["variables"]}, "PatchVariablesRequest": {"type": "object", "properties": {"patchVariables": {"type": "string", "description": "Variables a actualizar en formato JSON"}, "merge": {"type": "boolean", "description": "Si true, hace merge profundo; si false, reemplaza", "default": false}}, "required": ["patchVariables"]}, "SetVariableRequest": {"type": "object", "properties": {"variableName": {"type": "string", "description": "Nombre de la variable"}, "variableValue": {"type": "string", "description": "Valor de la variable en formato JSON"}}, "required": ["variableName", "variableValue"]}, "Task": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Identificador único de la tarea"}, "processInstanceId": {"type": "string", "format": "uuid", "description": "ID de la instancia de proceso"}, "name": {"type": "string", "description": "Nombre de la tarea"}, "description": {"type": "string", "description": "Descripción de la tarea"}, "formKey": {"type": "string", "description": "Clave del formulario asociado"}, "order": {"type": "integer", "description": "Orden de la tarea en el proceso"}, "assignedTo": {"type": "string", "description": "Usuario asignado a la tarea", "nullable": true}, "status": {"$ref": "#/components/schemas/TaskStatus"}, "inputs": {"type": "string", "description": "Datos de entrada en formato JSON"}, "outputs": {"type": "string", "description": "Datos de salida en formato JSON", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha de creación"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Fecha de última actualización"}, "claimedAt": {"type": "string", "format": "date-time", "description": "Fecha de reclamación", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON> de <PERSON>pletad<PERSON>", "nullable": true}}, "required": ["id", "processInstanceId", "name", "formKey", "order", "status", "createdAt", "updatedAt"]}, "CreateTaskRequest": {"type": "object", "properties": {"processInstanceId": {"type": "string", "format": "uuid", "description": "ID de la instancia de proceso"}, "name": {"type": "string", "description": "Nombre de la tarea"}, "description": {"type": "string", "description": "Descripción de la tarea"}, "formKey": {"type": "string", "description": "Clave del formulario asociado"}, "order": {"type": "integer", "description": "Orden de la tarea en el proceso"}, "inputs": {"type": "string", "description": "Datos de entrada en formato JSON"}}, "required": ["processInstanceId", "name", "formKey", "order"]}, "UpdateTaskRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Nombre de la tarea"}, "description": {"type": "string", "description": "Descripción de la tarea"}, "formKey": {"type": "string", "description": "Clave del formulario asociado"}, "order": {"type": "integer", "description": "Orden de la tarea en el proceso"}, "inputs": {"type": "string", "description": "Datos de entrada en formato JSON"}}}, "TaskStatus": {"type": "string", "enum": ["CREATED", "ASSIGNED", "IN_PROGRESS", "COMPLETED", "CANCELLED"], "description": "Estado de la tarea"}, "AssignTaskRequest": {"type": "object", "properties": {"assignedTo": {"type": "string", "description": "Usuario a <PERSON>ignar"}}, "required": ["assignedTo"]}, "ClaimTaskRequest": {"type": "object", "properties": {"username": {"type": "string", "description": "Usuario que reclama la tarea"}}, "required": ["username"]}, "CompleteTaskRequest": {"type": "object", "properties": {"username": {"type": "string", "description": "Usuario que completa la tarea"}, "outputs": {"type": "string", "description": "Datos de salida en formato JSON"}}, "required": ["username"]}, "Subtask": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Identificador único de la subtarea"}, "taskId": {"type": "string", "format": "uuid", "description": "ID de la tarea padre"}, "name": {"type": "string", "description": "Nombre de la subtarea"}, "description": {"type": "string", "description": "Descripción de la subtarea"}, "formKey": {"type": "string", "description": "Clave del formulario asociado"}, "order": {"type": "integer", "description": "Orden de la subtarea"}, "status": {"$ref": "#/components/schemas/SubtaskStatus"}, "inputs": {"type": "string", "description": "Datos de entrada en formato JSON"}, "outputs": {"type": "string", "description": "Datos de salida en formato JSON", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha de creación"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Fecha de última actualización"}, "completedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON> de <PERSON>pletad<PERSON>", "nullable": true}}, "required": ["id", "taskId", "name", "formKey", "order", "status", "createdAt", "updatedAt"]}, "SubtaskStatus": {"type": "string", "enum": ["CREATED", "COMPLETED", "CANCELLED"], "description": "Estado de la subtarea"}, "CreateSubtaskRequest": {"type": "object", "properties": {"taskId": {"type": "string", "format": "uuid", "description": "ID de la tarea padre"}, "name": {"type": "string", "description": "Nombre de la subtarea"}, "description": {"type": "string", "description": "Descripción de la subtarea"}, "formKey": {"type": "string", "description": "Clave del formulario asociado"}, "order": {"type": "integer", "description": "Orden de la subtarea"}, "inputs": {"type": "string", "description": "Datos de entrada en formato JSON"}}, "required": ["taskId", "name", "formKey", "order"]}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "Código de estado HTTP"}, "error": {"type": "string", "description": "Tipo de error"}, "message": {"type": "string", "description": "Mensaje descriptivo del error"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp del error"}}, "required": ["status", "error", "message", "timestamp"]}}, "parameters": {"UuidPathParam": {"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "Identificador UUID del recurso"}}, "responses": {"BadRequest": {"description": "Solicitud inválida", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "Recurso no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Conflict": {"description": "Conflicto en la operación", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "Error interno del servidor", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "tags": [{"name": "Process Definitions", "description": "Gestión de plantillas de proceso"}, {"name": "Process Instances", "description": "Gestión de instancias de proceso"}, {"name": "Process Variables", "description": "Gestión de variables de proceso"}, {"name": "Tasks", "description": "Gestión de tareas"}, {"name": "Subtasks", "description": "Gestión de subtareas"}, {"name": "User Tasks", "description": "<PERSON><PERSON><PERSON> por usuario"}]}