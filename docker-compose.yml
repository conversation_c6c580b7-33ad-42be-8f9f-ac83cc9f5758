services:
  postgres:
    image: postgres:15-alpine
    container_name: proezedure-postgres
    environment:
      POSTGRES_DB: proezedure
      POSTGRES_USER: proezedure_user
      POSTGRES_PASSWORD: proezedure_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - proezedure-network

volumes:
  postgres_data:

networks:
  proezedure-network:
    driver: bridge
